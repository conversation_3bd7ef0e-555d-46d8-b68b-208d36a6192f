"use client";

import { motion } from 'framer-motion';
import Button from '../Button';
import SnakeGame from './games/SnakeGame';
import GigHuntGame from './games/GigHuntGame';

const GameScreen = ({ game, onBack, isTransitioning }) => {
  const renderGame = () => {
    switch (game.id) {
      case 'snake':
        return <SnakeGame />;
      case 'gighunt':
        return <GigHuntGame />;
      // Future games will be added here
      // case 'tetris':
      //   return <TetrisGame />;
      // case 'duckhunt':
      //   return <DuckHuntGame />;
      default:
        return (
          <div className="flex items-center justify-center h-full">
            <p className="text-secondary text-xl">Game not found</p>
          </div>
        );
    }
  };

  return (
    <motion.div
      className="w-full max-w-4xl"
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{
        opacity: isTransitioning ? 0 : 1,
        scale: isTransitioning ? 0.8 : 1
      }}
      transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
    >
      <div className="space-y-6">
        {/* CRT Game Screen */}
        <div className="crt-screen active bg-black rounded-3xl overflow-hidden shadow-2xl" style={{ aspectRatio: '4/3' }}>
          {/* Game Content */}
          <div className="w-full h-full relative crt-content">
            {renderGame()}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default GameScreen;
